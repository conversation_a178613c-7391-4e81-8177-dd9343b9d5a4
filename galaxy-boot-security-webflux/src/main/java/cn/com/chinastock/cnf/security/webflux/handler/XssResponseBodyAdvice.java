package cn.com.chinastock.cnf.security.webflux.handler;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.output.FasterJsonFilterUtil;
import cn.com.chinastock.cnf.security.output.FastJsonFilterUtil;
import org.springframework.web.util.HtmlUtils;

/**
 * WebFlux XSS 响应体处理器
 *
 * <p>该处理器在 WebFlux 环境下对响应体进行 XSS 过滤，支持：</p>
 * <ul>
 *     <li>JSON 响应体过滤（FastJson 和 Jackson）</li>
 *     <li>字符串响应体 HTML 转义</li>
 *     <li>响应式流处理</li>
 * </ul>
 *
 * <AUTHOR> Assistant
 */
public class XssResponseBodyAdvice {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(XssResponseBodyAdvice.class);

    private final CopiedFastJsonProperties fastJsonProperties;

    public XssResponseBodyAdvice(CopiedFastJsonProperties fastJsonProperties) {
        this.fastJsonProperties = fastJsonProperties;
        logger.info(LogCategory.FRAMEWORK_LOG, "XSS Response Body Advice initialized for WebFlux");
    }

    /**
     * 处理响应体，在写入前进行 XSS 过滤处理
     *
     * @param body 响应体对象
     * @return 过滤后的响应体对象
     */
    public Object processResponseBody(Object body) {
        if (body == null) {
            return null;
        }

        // 处理非响应式类型
        return filterResponseBody(body);
    }

    /**
     * 过滤响应体内容
     *
     * @param body 响应体对象
     * @return 过滤后的响应体对象
     */
    private Object filterResponseBody(Object body) {
        if (body == null) {
            return null;
        }

        try {
            // 判断响应类型并进行相应处理
            if (isJsonResponse(body)) {
                return handleJsonResponse(body);
            } else if (body instanceof String) {
                return HtmlUtils.htmlEscape((String) body);
            }
            
            return body;
        } catch (Exception e) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to filter response body, using original content", e);
            return body;
        }
    }

    /**
     * 判断是否为 JSON 响应
     *
     * @param body 响应体对象
     * @return 如果是 JSON 响应返回 true，否则返回 false
     */
    private boolean isJsonResponse(Object body) {
        // 简单判断：如果不是基本类型，认为是 JSON 对象
        return !(body instanceof String) &&
               !(body instanceof Number) &&
               !(body instanceof Boolean) &&
               !(body instanceof Character);
    }

    /**
     * 处理 JSON 响应
     *
     * @param body 响应体对象
     * @return 处理后的响应体对象
     */
    private Object handleJsonResponse(Object body) {
        try {
            // 尝试使用 FastJson 处理
            if (fastJsonProperties != null) {
                return FastJsonFilterUtil.handleFastJsonResponse(body, fastJsonProperties);
            } else {
                // 使用 Jackson 处理
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            }
        } catch (Exception e) {
            logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to handle JSON response with XSS filter", e);
            // 如果处理失败，尝试用 Jackson 处理
            try {
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            } catch (Exception ex) {
                logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to handle JSON response with Jackson filter", ex);
                return body;
            }
        }
    }
}
