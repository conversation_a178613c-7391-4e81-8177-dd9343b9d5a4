package cn.com.chinastock.cnf.security.webflux.filter;

import cn.com.chinastock.cnf.core.log.IGalaxyLogger;
import cn.com.chinastock.cnf.core.log.GalaxyLoggerFactory;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.input.XssSanitizerUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.Set;

/**
 * WebFlux XSS 输入过滤器
 * 
 * <p>该过滤器在 WebFlux 环境下提供 XSS 防护功能，包括：</p>
 * <ul>
 *     <li>请求参数 XSS 过滤</li>
 *     <li>请求头 XSS 过滤</li>
 *     <li>请求体 XSS 过滤（JSON 格式）</li>
 *     <li>静态资源跳过处理</li>
 * </ul>
 * 
 * <AUTHOR> Assistant
 */
public class XssWebFilter implements WebFilter, Ordered {

    private static final IGalaxyLogger logger = GalaxyLoggerFactory.getLogger(XssWebFilter.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    
    private static final Set<String> STATIC_RESOURCE_EXTENSIONS = Set.of(
            ".css", ".js", ".png", ".jpg", ".gif", ".ico", ".svg",
            ".woff", ".woff2", ".ttf", ".eot", ".mp3", ".mp4",
            ".pdf", ".xlsx", ".doc", ".docx", ".xls", ".ppt", ".pptx", ".zip"
    );

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE + 1;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        
        // 跳过静态资源
        if (isStaticResource(request.getURI().getPath())) {
            return chain.filter(exchange);
        }

        // 创建 XSS 防护的请求装饰器
        XssServerHttpRequestDecorator xssRequest = new XssServerHttpRequestDecorator(request);
        
        return chain.filter(exchange.mutate().request(xssRequest).build());
    }

    /**
     * 判断是否为静态资源
     *
     * @param path 请求路径
     * @return 如果是静态资源返回 true，否则返回 false
     */
    private boolean isStaticResource(String path) {
        if (path == null) {
            return false;
        }
        
        return STATIC_RESOURCE_EXTENSIONS.stream()
                .anyMatch(ext -> path.toLowerCase().endsWith(ext));
    }

    /**
     * XSS 防护的 ServerHttpRequest 装饰器
     */
    private static class XssServerHttpRequestDecorator extends ServerHttpRequestDecorator {
        
        private final ServerHttpRequest delegate;
        private final DataBufferFactory dataBufferFactory;

        public XssServerHttpRequestDecorator(ServerHttpRequest delegate) {
            super(delegate);
            this.delegate = delegate;
            // 使用默认的 DataBufferFactory
            this.dataBufferFactory = delegate.getBody().next()
                    .cast(DataBuffer.class)
                    .map(DataBuffer::factory)
                    .blockOptional()
                    .orElse(org.springframework.core.io.buffer.DefaultDataBufferFactory.sharedInstance);
        }

        @Override
        public HttpHeaders getHeaders() {
            HttpHeaders originalHeaders = super.getHeaders();
            HttpHeaders sanitizedHeaders = new HttpHeaders();
            
            originalHeaders.forEach((name, values) -> {
                values.forEach(value -> {
                    String sanitizedValue = XssSanitizerUtil.stripXSS(value);
                    sanitizedHeaders.add(name, sanitizedValue);
                });
            });
            
            return sanitizedHeaders;
        }

        @Override
        public Flux<DataBuffer> getBody() {
            MediaType contentType = delegate.getHeaders().getContentType();
            
            // 只处理 JSON 类型的请求体
            if (contentType == null || !MediaType.APPLICATION_JSON.isCompatibleWith(contentType)) {
                return super.getBody();
            }

            return super.getBody()
                    .collectList()
                    .flatMapMany(dataBuffers -> {
                        if (dataBuffers.isEmpty()) {
                            return Flux.empty();
                        }
                        
                        return sanitizeJsonBody(dataBuffers);
                    });
        }

        /**
         * 对 JSON 请求体进行 XSS 过滤
         *
         * @param dataBuffers 数据缓冲区列表
         * @return 过滤后的数据缓冲区流
         */
        private Flux<DataBuffer> sanitizeJsonBody(java.util.List<DataBuffer> dataBuffers) {
            return Mono.fromCallable(() -> {
                // 合并所有 DataBuffer
                DataBuffer joinedBuffer = dataBufferFactory.join(dataBuffers);
                
                try {
                    // 读取 JSON 内容
                    byte[] bytes = new byte[joinedBuffer.readableByteCount()];
                    joinedBuffer.read(bytes);
                    String jsonContent = new String(bytes, StandardCharsets.UTF_8);
                    
                    if (jsonContent.trim().isEmpty()) {
                        return dataBufferFactory.wrap(bytes);
                    }
                    
                    // 解析并过滤 JSON
                    JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonContent);
                    JsonNode sanitizedNode = sanitizeJsonNode(jsonNode);
                    String sanitizedJson = OBJECT_MAPPER.writeValueAsString(sanitizedNode);
                    
                    return dataBufferFactory.wrap(sanitizedJson.getBytes(StandardCharsets.UTF_8));
                    
                } catch (Exception e) {
                    logger.warn(LogCategory.FRAMEWORK_LOG, "Failed to sanitize JSON body, using original content", e);
                    // 如果解析失败，返回原始内容
                    byte[] bytes = new byte[joinedBuffer.readableByteCount()];
                    joinedBuffer.read(bytes);
                    return dataBufferFactory.wrap(bytes);
                } finally {
                    // 释放原始 DataBuffer
                    DataBufferUtils.release(joinedBuffer);
                    dataBuffers.forEach(DataBufferUtils::release);
                }
            })
            .flux()
            .cast(DataBuffer.class);
        }

        /**
         * 递归过滤 JSON 节点
         *
         * @param node JSON 节点
         * @return 过滤后的 JSON 节点
         */
        private JsonNode sanitizeJsonNode(JsonNode node) {
            if (node == null) {
                return null;
            }
            
            if (node.isTextual()) {
                String sanitizedText = XssSanitizerUtil.stripXSS(node.asText());
                return new TextNode(sanitizedText);
            } else if (node.isObject()) {
                ObjectNode objectNode = OBJECT_MAPPER.createObjectNode();
                node.fields().forEachRemaining(entry -> {
                    String key = entry.getKey();
                    JsonNode value = entry.getValue();
                    objectNode.set(key, sanitizeJsonNode(value));
                });
                return objectNode;
            } else if (node.isArray()) {
                ArrayNode arrayNode = OBJECT_MAPPER.createArrayNode();
                for (JsonNode element : node) {
                    arrayNode.add(sanitizeJsonNode(element));
                }
                return arrayNode;
            }
            
            // 其他类型（数字、布尔值等）直接返回
            return node;
        }
    }
}
